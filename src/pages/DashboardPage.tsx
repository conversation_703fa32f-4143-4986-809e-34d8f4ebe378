import { useState, useMemo, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import UnifiedLayout from "@/components/layout/UnifiedLayout";
import {
  Users, Calendar, TrendingUp, Percent, User, ShieldCheck, Trophy, History, Sparkles, ArrowRight,
  Plus, UserPlus, CalendarPlus, Search, LayoutDashboard, Medal, Handshake, Shuffle, Crown,
  UsersRound, ChevronDown, ChevronUp, Award, Flame
} from "lucide-react";
import { useGroup } from "@/lib/context/GroupContext";

// Enhanced components
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { ResponsiveTable } from "@/components/ui/responsive-table";
import { DashboardSkeleton, PlayerCardSkeleton } from "@/components/ui/skeletons";
import { AnimatedButton } from "@/components/ui/animated-button";
import { useEnhancedToast } from "@/hooks/use-enhanced-toast";
import { EnhancedInput } from "@/components/ui/enhanced-input";
import { EmptyState } from "@/components/ui/empty-state";
import { useUserPreferences } from "@/context/UserPreferencesContext";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { format, parseISO } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Dashboard tab components
import DashboardLeaderboard from "@/components/dashboard/DashboardLeaderboard";
import DashboardChemistry from "@/components/dashboard/DashboardChemistry";
import DashboardTeamGenerator from "@/components/dashboard/DashboardTeamGenerator";

// localStorage Keys
const PLAYERS_STORAGE_KEY = 'soccerPlayersData';
const MATCHES_STORAGE_KEY = 'soccerMatchesData';

// --- Data Loading ---

interface Player {
  id: number;
  name: string;
  skills: number;
  effort: number;
  stamina: number;
  group_id?: string;
}

interface Goalscorer { team: 'A' | 'B'; playerId: number; }
interface Match { id: number; date: Date; teamA: number[]; teamB: number[]; scoreA: number | null; scoreB: number | null; winner?: 'A' | 'B' | 'Draw'; goalscorers?: Goalscorer[]; youtubeLink?: string; group_id?: string; }
interface ChemistryPair { player1: number; player2: number; played: number; wins: number; winRate: number; }
interface BestChemistry {
  players: number[];
  played: number;
  wins: number;
  winRate: number;
}

interface PlayerChemistryStats {
  duos: BestChemistry[];
  trios: BestChemistry[];
  quads: BestChemistry[];
}

interface Group {
  id: string;
  name: string;
  created_by: string;
  created_at: string;
}

// Function to load data from localStorage
function loadData<T>(key: string, fallback: T[]): T[] {
    try {
      const storedData = typeof window !== 'undefined' ? localStorage.getItem(key) : null;
      if (storedData) {
          const parsedData = JSON.parse(storedData) as any[];
          // Special handling for dates in matches
          if (key === MATCHES_STORAGE_KEY) {
              // Make date parsing more robust
              return parsedData.map(item => ({
                  ...item,
                  date: item.date ? parseISO(item.date) : new Date(0) // Use epoch if date is missing/invalid
              })) as T[];
          }
          return parsedData as T[];
      }
    } catch (error) {
      console.error(`Error reading ${key} from localStorage:`, error);
    }
    return fallback;
}

// Load players and matches data
const playersData = loadData<Player>(PLAYERS_STORAGE_KEY, []);
const matchesData = loadData<Match>(MATCHES_STORAGE_KEY, []);

// Placeholder for chemistry data
const chemistryData: ChemistryPair[] = [
  // This should likely be calculated from matchesData or loaded too
];

// --- Helper Functions ---

const calculateAverageRating = (player: Omit<Player, 'id' | 'name'>): number => {
  if (!player || typeof player.skills !== 'number' || typeof player.effort !== 'number' || typeof player.stamina !== 'number') return 0;
  return Math.round((player.skills + player.effort + player.stamina) / 3);
};

// Define a type for a completed World Cup Run
interface CompletedWorldCupRun {
  date: Date; // Date when the World Cup was won (date of the last knockout match)
}

// Define a type for the World Cup Run result
interface WorldCupRunResult {
  isWorldChampion: boolean;
  status: 'champion' | 'qualified' | 'in-progress' | 'eliminated' | 'not-started';
  groupStagePoints: number;
  groupStageMatchesPlayed: number;
  pointsNeededToQualify: number;
  knockoutMatchesWon: number;
  knockoutMatchesNeeded: number;
  currentRun: number; // How many World Cups won
  completedRuns: CompletedWorldCupRun[]; // Array of completed World Cup Runs with dates
}

// Calculate World Cup Run stat based on tournament simulation
const calculateWorldCupRun = (matches: Match[], playerId: number): WorldCupRunResult => {
  // Default result with no matches
  const defaultResult: WorldCupRunResult = {
    isWorldChampion: false,
    status: 'not-started',
    groupStagePoints: 0,
    groupStageMatchesPlayed: 0,
    pointsNeededToQualify: 4,
    knockoutMatchesWon: 0,
    knockoutMatchesNeeded: 4,
    currentRun: 0,
    completedRuns: []
  };

  // Get matches where this player participated, sorted by date (newest first)
  const playerMatches = matches
    .filter(match => (match.teamA?.includes(playerId) || match.teamB?.includes(playerId)))
    .sort((a, b) => {
      const dateA = a.date instanceof Date ? a.date : new Date(a.date);
      const dateB = b.date instanceof Date ? b.date : new Date(b.date);
      return dateB.getTime() - dateA.getTime(); // Sort newest first
    });

  // If no matches, return default result
  if (playerMatches.length === 0) return defaultResult;

  // Helper function to check if player won a match
  const didPlayerWin = (match: Match): boolean => {
    const isInTeamA = match.teamA?.includes(playerId);
    return (isInTeamA && match.winner === 'A') || (!isInTeamA && match.winner === 'B');
  };

  // Helper function to check if player lost a match
  const didPlayerLose = (match: Match): boolean => {
    const isInTeamA = match.teamA?.includes(playerId);
    return (isInTeamA && match.winner === 'B') || (!isInTeamA && match.winner === 'A');
  };

  // Helper function to check if match was a draw
  const isMatchDraw = (match: Match): boolean => {
    return match.winner === 'Draw';
  };

  // Helper function to check if a group stage is valid (at least 2 wins, or 1 win and 1 draw)
  const isValidGroupStage = (matches: Match[]): boolean => {
    if (matches.length !== 3) return false;

    let wins = 0;
    let draws = 0;

    matches.forEach(match => {
      if (didPlayerWin(match)) wins++;
      else if (isMatchDraw(match)) draws++;
    });

    // Valid if: 2+ wins OR 1 win + 1+ draws
    return wins >= 2 || (wins === 1 && draws >= 1);
  };

  // Helper function to calculate points for a group of matches
  const calculateGroupPoints = (matches: Match[]): number => {
    return matches.reduce((points, match) => {
      if (didPlayerWin(match)) return points + 3;
      if (isMatchDraw(match)) return points + 1;
      return points;
    }, 0);
  };

  // Track current World Cup run count and completed runs
  let worldCupRunCount = 0;
  let currentStatus: WorldCupRunResult['status'] = 'not-started';
  let currentGroupStagePoints = 0;
  let currentGroupStageMatchesPlayed = 0;
  let currentKnockoutMatchesWon = 0;
  let completedRuns: CompletedWorldCupRun[] = [];

  // Create a chronologically ordered copy of the matches (oldest first)
  // This ensures we evaluate the knockout stage chronologically after the group stage
  const chronologicalMatches = [...playerMatches].reverse();

  // Scan for completed World Cup runs and current status
  let i = 0;
  while (i <= chronologicalMatches.length - 3) {
    // Check if we have a valid group stage (3 consecutive matches)
    const potentialGroupStage = chronologicalMatches.slice(i, i + 3);

    if (isValidGroupStage(potentialGroupStage)) {
      // Found a valid group stage, now check for knockout matches that follow
      // Make sure there are enough matches after the group stage
      if (i + 3 + 4 <= chronologicalMatches.length) {
        // Get the 4 matches that follow the group stage
        const knockoutMatches = chronologicalMatches.slice(i + 3, i + 3 + 4);

        // Check if all knockout matches were wins
        const allKnockoutWins = knockoutMatches.every(match => didPlayerWin(match));

        if (allKnockoutWins) {
          // Found a valid World Cup run!
          worldCupRunCount++;

          // Get the date of the last knockout match (when the World Cup was won)
          const lastKnockoutMatch = knockoutMatches[knockoutMatches.length - 1];
          const winDate = lastKnockoutMatch.date instanceof Date
            ? lastKnockoutMatch.date
            : new Date(lastKnockoutMatch.date);

          // Add to completed runs
          completedRuns.push({ date: winDate });

          // Skip this entire run
          i += 7;
          continue;
        }
      }
    }

    // Move to the next potential starting point
    i++;
  }

  // Now determine the current status based on the most recent matches
  // Check if the last 3 matches form a valid group stage
  if (playerMatches.length >= 3) {
    const lastThreeMatches = playerMatches.slice(0, 3);
    const isValidGroup = isValidGroupStage(lastThreeMatches);
    const groupPoints = calculateGroupPoints(lastThreeMatches);

    if (isValidGroup) {
      // We have a valid group stage in the last 3 matches
      // For the current status, we don't need to look for knockout matches
      // since these are the most recent matches - the knockout stage would be in the future
      currentStatus = 'qualified';
      currentGroupStagePoints = groupPoints;
      currentGroupStageMatchesPlayed = 3;
      currentKnockoutMatchesWon = 0; // No knockout matches for the current run yet
    } else {
      // The last 3 matches don't form a valid group stage
      // Check if they're in progress for a group stage
      currentStatus = 'in-progress';
      currentGroupStagePoints = groupPoints;
      currentGroupStageMatchesPlayed = 3;
    }
  } else if (playerMatches.length > 0) {
    // Less than 3 matches, but some matches exist - in progress
    const points = calculateGroupPoints(playerMatches);
    currentStatus = 'in-progress';
    currentGroupStagePoints = points;
    currentGroupStageMatchesPlayed = playerMatches.length;
  }

  // Check if player is eliminated (lost last two matches)
  if (playerMatches.length >= 2) {
    const isCurrentlyEliminated = didPlayerLose(playerMatches[0]) && didPlayerLose(playerMatches[1]);
    if (isCurrentlyEliminated && currentStatus === 'in-progress') {
      currentStatus = 'eliminated';
    }
  }

  // Calculate points needed to qualify - need at least 4 points to qualify
  // 4 points can be achieved with 1 win and 1 draw (3 + 1) or 2 wins (3 + 3)
  const pointsNeededToQualify = Math.max(0, 4 - currentGroupStagePoints);

  return {
    isWorldChampion: currentStatus === 'champion',
    status: currentStatus,
    groupStagePoints: currentGroupStagePoints,
    groupStageMatchesPlayed: currentGroupStageMatchesPlayed,
    pointsNeededToQualify,
    knockoutMatchesWon: currentKnockoutMatchesWon,
    knockoutMatchesNeeded: 4,
    currentRun: worldCupRunCount,
    completedRuns: completedRuns.sort((a, b) => b.date.getTime() - a.date.getTime()) // Sort by date, newest first
  };
};

// Calculate the best win streak for a player
const calculateBestWinStreak = (matches: Match[], playerId: number): number => {
  // Get matches where this player participated, sorted by date (oldest first)
  const playerMatches = matches
    .filter(match => (match.teamA?.includes(playerId) || match.teamB?.includes(playerId)))
    .sort((a, b) => {
      const dateA = a.date instanceof Date ? a.date : new Date(a.date);
      const dateB = b.date instanceof Date ? b.date : new Date(b.date);
      return dateA.getTime() - dateB.getTime(); // Sort oldest first
    });

  if (playerMatches.length === 0) return 0;

  let bestStreak = 0;
  let currentStreak = 0;

  // Process all matches to find the best streak
  for (let i = 0; i < playerMatches.length; i++) {
    const match = playerMatches[i];
    const isInTeamA = match.teamA?.includes(playerId);
    const isWinner = (isInTeamA && match.winner === 'A') || (!isInTeamA && match.winner === 'B');

    if (isWinner) {
      // Increment current streak
      currentStreak++;
      // Update best streak if current is better
      bestStreak = Math.max(bestStreak, currentStreak);
    } else {
      // Reset streak on loss or draw
      currentStreak = 0;
    }
  }

  return bestStreak;
};

const renderScoreDisplay = (match: Match) => {
  if (match.scoreA !== null && match.scoreB !== null) {
    return <>{match.scoreA} - {match.scoreB}</>;
  } else {
    if (match.winner === 'A') return <span className="text-green-600">TEAM A WIN</span>;
    if (match.winner === 'B') return <span className="text-green-600">TEAM B WIN</span>;
    if (match.winner === 'Draw') return <span className="text-yellow-600">DRAW</span>;
    return "? - ?";
  }
};

// Add this after the other helper functions
function getCombinations<T>(array: T[], k: number): T[][] {
  const result: T[][] = [];
  function combine(start: number, currentCombination: T[]) {
    if (currentCombination.length === k) {
      result.push([...currentCombination]);
      return;
    }
    for (let i = start; i < array.length; i++) {
      currentCombination.push(array[i]);
      combine(i + 1, currentCombination);
      currentCombination.pop();
    }
  }
  combine(0, []);
  return result;
}

// --- Component ---

interface ChemistryStats {
  duos: { played: number; wins: number; winRate: number; };
  trios: { played: number; wins: number; winRate: number; };
  quads: { played: number; wins: number; winRate: number; };
}

const calculatePlayerChemistry = (matches: Match[], playerId: number): ChemistryStats => {
  const stats = {
    duos: { played: 0, wins: 0, winRate: 0 },
    trios: { played: 0, wins: 0, winRate: 0 },
    quads: { played: 0, wins: 0, winRate: 0 }
  };

  matches.forEach(match => {
    const teamA = match.teamA || [];
    const teamB = match.teamB || [];
    const isInTeamA = teamA.includes(playerId);
    const isInTeamB = teamB.includes(playerId);
    const team = isInTeamA ? teamA : isInTeamB ? teamB : null;
    const isWinner = (isInTeamA && match.winner === 'A') || (isInTeamB && match.winner === 'B');

    if (team) {
      const teamSize = team.length;
      if (teamSize >= 2) stats.duos.played++;
      if (teamSize >= 3) stats.trios.played++;
      if (teamSize >= 4) stats.quads.played++;

      if (isWinner) {
        if (teamSize >= 2) stats.duos.wins++;
        if (teamSize >= 3) stats.trios.wins++;
        if (teamSize >= 4) stats.quads.wins++;
      }
    }
  });

  // Calculate win rates
  stats.duos.winRate = stats.duos.played > 0 ? (stats.duos.wins / stats.duos.played) * 100 : 0;
  stats.trios.winRate = stats.trios.played > 0 ? (stats.trios.wins / stats.trios.played) * 100 : 0;
  stats.quads.winRate = stats.quads.played > 0 ? (stats.quads.wins / stats.quads.played) * 100 : 0;

  return stats;
};

const calculatePlayerBestChemistry = (matches: Match[], playerId: number, minGamesForChemistry: string): PlayerChemistryStats => {
  const stats = {
    duos: new Map<string, {players: number[], played: number, wins: number}>(),
    trios: new Map<string, {players: number[], played: number, wins: number}>(),
    quads: new Map<string, {players: number[], played: number, wins: number}>()
  };

  matches.forEach(match => {
    const teamA = match.teamA || [];
    const teamB = match.teamB || [];
    const isInTeamA = teamA.includes(playerId);
    const isInTeamB = teamB.includes(playerId);
    const team = isInTeamA ? teamA : isInTeamB ? teamB : null;
    const isWinner = (isInTeamA && match.winner === 'A') || (isInTeamB && match.winner === 'B');

    if (team) {
      // Process all combinations that include the selected player
      const processGroup = (size: 2 | 3 | 4, statsMap: Map<string, any>) => {
        if (team.length >= size) {
          const combinations = getCombinations(team.filter(id => id !== playerId), size - 1);
          combinations.forEach(combo => {
            const group = [...combo, playerId].sort((a, b) => a - b);
            const key = group.join('-');
            const existing = statsMap.get(key) || { players: group, played: 0, wins: 0 };
            existing.played++;
            if (isWinner) existing.wins++;
            statsMap.set(key, existing);
          });
        }
      };

      processGroup(2, stats.duos);
      processGroup(3, stats.trios);
      processGroup(4, stats.quads);
    }
  });

  // Convert to arrays and calculate win rates
  const processStats = (statsMap: Map<string, any>): BestChemistry[] => {
    const minGames = parseInt(minGamesForChemistry, 10);
    return Array.from(statsMap.values())
      .filter(stat => stat.played >= minGames)
      .map(stat => ({
        ...stat,
        winRate: (stat.played > 0 ? (stat.wins / stat.played) * 100 : 0)
      }))
      .sort((a, b) => b.winRate - a.winRate)
      .slice(0, 1); // Get only the top 1 best pair
  };

  return {
    duos: processStats(stats.duos),
    trios: processStats(stats.trios),
    quads: processStats(stats.quads)
  };
};

// We're now using the currentGroup from the GroupContext instead of localStorage

const DashboardPage = () => {
  const { t } = useTranslation();
  const { user, hasLoadedGroups } = useAuth();
  const { currentGroup, isLoading: isGroupLoading } = useGroup();
  const { toast } = useToast();
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState(true);
  const [players, setPlayers] = useState<Player[]>([]);
  const [matches, setMatches] = useState<Match[]>([]);
  const [selectedPlayerId, setSelectedPlayerId] = useState<string>("");
  const [selectedPlayerData, setSelectedPlayerData] = useState<any | null>(null);
  const [isPlayerDataLoading, setIsPlayerDataLoading] = useState(false);
  const [minGamesForWinRate, setMinGamesForWinRate] = useState<string>("0");
  const [minGamesForLeaderboard, setMinGamesForLeaderboard] = useState<string>("0");
  const [activeTab, setActiveTab] = useState<string>("overview");
  const { preferences } = useUserPreferences();

  // Update playerStatsWithWinRate to use state
  const playerStatsWithWinRate = useMemo(() => {
    const statsMap: { [key: number]: { played: number; wins: number; draws: number; losses: number } } = {};

    players.forEach(p => {
      statsMap[p.id] = { played: 0, wins: 0, draws: 0, losses: 0 };
    });

    matches.forEach(m => {
      const involvedPlayers = [...(m.teamA || []), ...(m.teamB || [])];
      involvedPlayers.forEach(playerId => {
        if (statsMap[playerId]) {
          statsMap[playerId].played++;
          const isOnTeamA = m.teamA?.includes(playerId);
          if (m.winner === 'Draw') {
            statsMap[playerId].draws++;
          } else if ((isOnTeamA && m.winner === 'A') || (!isOnTeamA && m.winner === 'B')) {
            statsMap[playerId].wins++;
          } else if (m.winner) {
            statsMap[playerId].losses++;
          }
        }
      });
    });

    return players.map(player => {
      const stats = statsMap[player.id] ?? { played: 0, wins: 0, draws: 0, losses: 0 };
      const winRate = stats.played > 0 ? (stats.wins / stats.played) * 100 : 0;
      return { ...player, ...stats, winRate };
    });

  }, [players, matches]);

  // Calculate Top Win Rate player
  const topWinRatePlayer = useMemo(() => {
    const minGames = parseInt(minGamesForWinRate, 10);
    const eligiblePlayers = playerStatsWithWinRate.filter(p => p.played >= minGames);
    if (eligiblePlayers.length === 0) return null;
    return [...eligiblePlayers].sort((a, b) => b.winRate - a.winRate)[0];
  }, [minGamesForWinRate, playerStatsWithWinRate]);

  // Calculate Top Leaderboard player (by win rate)
  const topLeaderboardPlayer = useMemo(() => {
    const minGames = parseInt(minGamesForLeaderboard, 10);
    const eligiblePlayers = playerStatsWithWinRate.filter(p => p.played >= minGames);
    if (eligiblePlayers.length === 0) return null;

    // Process players to include avgRating
    const playersWithRating = eligiblePlayers.map(player => ({
      ...player,
      avgRating: calculateAverageRating(player)
    }));

    // Sort by win rate
    return [...playersWithRating].sort((a, b) => b.winRate - a.winRate)[0];
  }, [minGamesForLeaderboard, playerStatsWithWinRate]);

  // Calculate best chemistry combinations (duos, trios, quads)
  const [minGamesForChemistry, setMinGamesForChemistry] = useState<string>("3");
  const bestChemistry = useMemo(() => {
    const minGames = parseInt(minGamesForChemistry, 10);

    // Helper function to get combinations
    const getCombinations = (arr: number[], size: number): number[][] => {
      const result: number[][] = [];

      function backtrack(start: number, current: number[]) {
        if (current.length === size) {
          result.push([...current]);
          return;
        }

        for (let i = start; i < arr.length; i++) {
          current.push(arr[i]);
          backtrack(i + 1, current);
          current.pop();
        }
      }

      backtrack(0, []);
      return result;
    };

    // Initialize maps for duos, trios, and quads
    const duosMap = new Map<string, { players: number[]; played: number; wins: number }>();
    const triosMap = new Map<string, { players: number[]; played: number; wins: number }>();
    const quadsMap = new Map<string, { players: number[]; played: number; wins: number }>();

    // Process each match
    matches.forEach(match => {
      const teamA = match.teamA || [];
      const teamB = match.teamB || [];

      // Process team A combinations
      if (teamA.length >= 2) {
        const duoCombos = getCombinations(teamA, 2);
        duoCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = duosMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'A') existing.wins++;
          duosMap.set(key, existing);
        });
      }

      if (teamA.length >= 3) {
        const trioCombos = getCombinations(teamA, 3);
        trioCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = triosMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'A') existing.wins++;
          triosMap.set(key, existing);
        });
      }

      if (teamA.length >= 4) {
        const quadCombos = getCombinations(teamA, 4);
        quadCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = quadsMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'A') existing.wins++;
          quadsMap.set(key, existing);
        });
      }

      // Process team B combinations
      if (teamB.length >= 2) {
        const duoCombos = getCombinations(teamB, 2);
        duoCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = duosMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'B') existing.wins++;
          duosMap.set(key, existing);
        });
      }

      if (teamB.length >= 3) {
        const trioCombos = getCombinations(teamB, 3);
        trioCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = triosMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'B') existing.wins++;
          triosMap.set(key, existing);
        });
      }

      if (teamB.length >= 4) {
        const quadCombos = getCombinations(teamB, 4);
        quadCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = quadsMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'B') existing.wins++;
          quadsMap.set(key, existing);
        });
      }
    });

    // Convert to arrays and calculate win rates
    const processChemistryData = (statsMap: Map<string, any>) => {
      return Array.from(statsMap.values())
        .filter(stat => stat.played >= minGames)
        .map(stat => ({
          ...stat,
          winRate: (stat.played > 0 ? (stat.wins / stat.played) * 100 : 0)
        }))
        .sort((a, b) => b.winRate - a.winRate)
        .slice(0, 1); // Get only the top 1
    };

    return {
      duos: processChemistryData(duosMap),
      trios: processChemistryData(triosMap),
      quads: processChemistryData(quadsMap)
    };
  }, [matches, minGamesForChemistry]);

  // Function to calculate player data
  const getPlayerData = (playerId: number) => {
    const player = playerStatsWithWinRate.find(p => p.id === playerId);
    if (!player) return null;

    // Calculate rank based on win rate and minimum games filter
    const minGames = parseInt(minGamesForLeaderboard, 10);
    const eligiblePlayers = playerStatsWithWinRate
      .filter(p => p.played >= minGames)
      .sort((a, b) => b.winRate - a.winRate);

    const rank = eligiblePlayers.findIndex(p => p.id === playerId) + 1;

    // Find last match played by this player from matches state
    const playerMatches = matches.filter(m => m.teamA?.includes(playerId) || m.teamB?.includes(playerId));
    const lastMatchPlayed = [...playerMatches]
      .filter(m => m.date instanceof Date && !isNaN(m.date.getTime()))
      .sort((a, b) => b.date.getTime() - a.date.getTime())[0];

    // Calculate chemistry stats
    const chemistryStats = calculatePlayerChemistry(matches, playerId);

    // Calculate best chemistry combinations
    const bestChemistry = calculatePlayerBestChemistry(matches, playerId, minGamesForChemistry);

    // Calculate World Cup Run stat
    const worldCupRunData = calculateWorldCupRun(matches, playerId);

    // Calculate best win streak
    const bestWinStreak = calculateBestWinStreak(matches, playerId);

    return {
      ...player,
      rank,
      lastMatch: lastMatchPlayed,
      avgRating: calculateAverageRating(player),
      chemistry: chemistryStats,
      bestChemistry,
      worldCupRunData,
      bestWinStreak
    };
  };

  // Memoized selected player data
  const selectedPlayerDataMemo = useMemo(() => {
    if (!selectedPlayerId) return null;
    const playerId = parseInt(selectedPlayerId, 10);
    return getPlayerData(playerId);
  }, [selectedPlayerId, playerStatsWithWinRate, players, matches, minGamesForChemistry]);

  // Find the last overall match
  const lastOverallMatch = useMemo(() => {
    if (matches.length === 0) return null;
    return [...matches]
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];
  }, [matches]);

  // Find the oldest match (for "Playing Since" info)
  const oldestMatch = useMemo(() => {
    if (matches.length === 0) return null;
    return [...matches]
      .sort((a, b) => {
        const dateA = a.date instanceof Date ? a.date :
                   a.match_date instanceof Date ? a.match_date :
                   new Date(a.match_date || a.date || Date.now());
        const dateB = b.date instanceof Date ? b.date :
                   b.match_date instanceof Date ? b.match_date :
                   new Date(b.match_date || b.date || Date.now());
        return dateA.getTime() - dateB.getTime();
      })[0];
  }, [matches]);

  // Update getPlayerName to use players state instead of playersData
  const getPlayerName = (id: number): string => {
    const player = players.find((p) => p.id === id);
    return player ? player.name : `Player ${id}`;
  };

  // Effect to update selectedPlayerData when a player is selected
  useEffect(() => {
    if (!selectedPlayerId) {
      setSelectedPlayerData(null);
      return;
    }

    setIsPlayerDataLoading(true);

    // Simulate a delay to show the skeleton loader
    setTimeout(() => {
      const playerId = parseInt(selectedPlayerId);
      const playerData = getPlayerData(playerId);
      setSelectedPlayerData(playerData);
      setIsPlayerDataLoading(false);
    }, 500);
  }, [selectedPlayerId, playerStatsWithWinRate, players, matches, minGamesForChemistry]);

  // Fetch players and matches when currentGroup changes
  // Using currentGroup.id as dependency instead of the entire object
  useEffect(() => {
    const fetchData = async () => {
      if (isGroupLoading) {
        console.log('Group context is still loading...');
        return;
      }

      if (!currentGroup) {
        console.log('No current group selected');
        setPlayers([]);
        setMatches([]);
        setIsLoading(false);
        return;
      }

      if (!hasLoadedGroups) {
        console.log('Groups have not been loaded yet');
        return;
      }

      console.log('Fetching data for group:', currentGroup.name, currentGroup.id);
      setIsLoading(true);
      try {
        // Always fetch fresh data from Supabase based on the current group
        const { data: playersResult, error: playersError } = await supabase
          .from('players')
          .select('*')
          .or(`group_id.eq.${currentGroup.id},group_id.is.null`);

        if (playersError) throw playersError;
        const playersData = playersResult as Player[];
        console.log('Fetched players:', playersData.length);

        const { data: matchesResult, error: matchesError } = await supabase
          .from('matches')
          .select('*')
          .or(`group_id.eq.${currentGroup.id},group_id.is.null`);

        if (matchesError) throw matchesError;
        let matchesData = matchesResult as Match[];
        console.log('Fetched matches:', matchesData.length);

        // Convert date strings to Date objects and normalize property names
        matchesData = matchesData.map(match => ({
          ...match,
          date: match.match_date ? new Date(match.match_date) : new Date(),
          teamA: match.teama || match.teamA || [],
          teamB: match.teamb || match.teamB || [],
          scoreA: match.scorea || match.scoreA,
          scoreB: match.scoreb || match.scoreB
        }));

        setPlayers(playersData);
        setMatches(matchesData);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast({
          title: t('errors.databaseError'),
          description: t('errors.tryAgain'),
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [currentGroup?.id, toast, isGroupLoading, hasLoadedGroups]);

  // Log when currentGroup changes - no dependencies to avoid infinite loops
  useEffect(() => {
    if (currentGroup) {
      console.log('Current group loaded:', currentGroup.name, currentGroup.id);
    }
  }, []);

  // Group management has been moved to the Group Selection page

  if (isLoading || isGroupLoading || !hasLoadedGroups) {
    return (
      <DashboardSkeleton />
    );
  }

  // Empty states for no players or matches
  const hasNoPlayers = players.length === 0;
  const hasNoMatches = matches.length === 0;

  return (
    <UnifiedLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">{t('nav.dashboard')}</h1>
        </div>

      {/* Show empty state if no data at all */}
      {hasNoPlayers && hasNoMatches ? (
        <EmptyState
          icon={Users}
          title={t('dashboard.welcome')}
          description={t('dashboard.getStarted')}
          actionLabel={t('dashboard.addFirstPlayer')}
          actionIcon={UserPlus}
          onAction={() => navigate('/players')}
          className="bg-card border-border py-12"
        />
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-4 mb-6">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger value="overview" className="dashboard-tab">
                    <LayoutDashboard className="h-4 w-4 md:mr-2" />
                    <span className="hidden md:inline tab-label">{t('dashboard.overview')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  {t('dashboard.overview')}
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger value="leaderboard" className="dashboard-tab">
                    <Medal className="h-4 w-4 md:mr-2" />
                    <span className="hidden md:inline tab-label">{t('nav.leaderboard')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  {t('nav.leaderboard')}
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger value="chemistry" className="dashboard-tab">
                    <Handshake className="h-4 w-4 md:mr-2" />
                    <span className="hidden md:inline tab-label">{t('nav.chemistry')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  {t('nav.chemistry')}
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger value="team-generator" className="dashboard-tab">
                    <Shuffle className="h-4 w-4 md:mr-2" />
                    <span className="hidden md:inline tab-label">{t('nav.teamGenerator')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  {t('nav.teamGenerator')}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </TabsList>

          <TabsContent value="overview">
            <div className="flex justify-start mb-4">
              <div className="flex items-center gap-2">
                <div className="text-sm text-muted-foreground">Min. Games:</div>
                <Select
                  value={minGamesForLeaderboard}
                  onValueChange={(value) => {
                    setMinGamesForLeaderboard(value);
                    setMinGamesForChemistry(value);
                    setMinGamesForWinRate(value);
                  }}
                >
                  <SelectTrigger className="h-8 text-sm w-[80px]">
                    <SelectValue placeholder="Min. Games" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">0</SelectItem>
                    <SelectItem value="3">3</SelectItem>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Group Stats Card - Enhanced */}
          <EnhancedCard hoverable className="lg:col-span-1 group">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="bg-gradient-to-br from-soccer-primary to-soccer-primary-light p-2 rounded-xl shadow-sm">
                    <Users className="h-5 w-5 text-white" />
                  </div>
                  <div className="font-semibold text-lg gradient-text-primary">{t('dashboard.groupStats')}</div>
                </div>
                <div className="flex gap-2">
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-8 w-8 rounded-full hover:bg-soccer-primary/10 hover:text-soccer-primary hover:scale-110 transition-all duration-300"
                    onClick={() => navigate('/players')}
                    title={t('players.addPlayer')}
                  >
                    <UserPlus className="h-4 w-4" />
                  </Button>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-8 w-8 rounded-full hover:bg-soccer-primary/10 hover:text-soccer-primary hover:scale-110 transition-all duration-300"
                    onClick={() => navigate('/matches')}
                    title={t('matches.addMatch')}
                  >
                    <CalendarPlus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="space-y-6">
                <div className="flex items-center gap-4 p-3 rounded-xl bg-gradient-to-r from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20 hover:border-soccer-primary/30 transition-all duration-300">
                  <div className="bg-gradient-to-br from-soccer-primary to-soccer-primary-light p-3 rounded-xl shadow-sm">
                    <Users className="h-5 w-5 text-white" aria-hidden="true" />
                  </div>
                  <div className="flex-1">
                    <div className="text-2xl font-bold text-soccer-primary">{players.length}</div>
                    <div className="text-sm font-medium text-muted-foreground">{t('dashboard.totalPlayers')}</div>
                  </div>
                  {hasNoPlayers && (
                    <AnimatedButton
                      size="sm"
                      onClick={() => navigate('/players')}
                      animation="scale"
                      icon={<UserPlus className="h-3 w-3" />}
                      variant="outline"
                      className="border-soccer-primary text-soccer-primary hover:bg-soccer-primary hover:text-white"
                    >
                      {t('players.addPlayer')}
                    </AnimatedButton>
                  )}
                </div>

                <div className="flex items-center gap-4 p-3 rounded-xl bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-950/20 dark:to-orange-950/20 border border-amber-200 dark:border-amber-800 hover:border-amber-300 dark:hover:border-amber-700 transition-all duration-300">
                  <div className="bg-gradient-to-br from-amber-500 to-orange-500 p-3 rounded-xl shadow-sm">
                    <Trophy className="h-5 w-5 text-white" aria-hidden="true" />
                  </div>
                  <div className="flex-1">
                    <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">{matches.length}</div>
                    <div className="text-sm font-medium text-muted-foreground">{t('dashboard.totalMatches')}</div>
                  </div>
                    {hasNoMatches && (
                      <AnimatedButton
                        size="sm"
                        onClick={() => navigate('/matches')}
                        animation="scale"
                        icon={<CalendarPlus className="h-3 w-3" />}
                        className="ml-auto"
                        variant="outline"
                      >
                        {t('matches.addMatch')}
                      </AnimatedButton>
                    )}
                  </div>

                  {oldestMatch && (
                    <div className="text-xs text-muted-foreground flex flex-col items-end">
                      <span>Playing Since:</span>
                      <span className="font-medium">
                        {format(
                          oldestMatch.date instanceof Date ? oldestMatch.date :
                          oldestMatch.match_date instanceof Date ? oldestMatch.match_date :
                          new Date(oldestMatch.match_date || oldestMatch.date || Date.now()),
                          "MMM d, yyyy"
                        )}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </EnhancedCard>

          {/* Leaderboard Leader Card - Enhanced */}
          <EnhancedCard hoverable className="lg:col-span-1 group">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="bg-gradient-to-br from-amber-500 to-yellow-500 p-2 rounded-xl shadow-sm">
                    <Crown className="h-5 w-5 text-white" />
                  </div>
                  <div className="font-semibold text-lg bg-gradient-to-r from-amber-600 to-yellow-600 bg-clip-text text-transparent">{t('dashboard.leader', 'Leader')}</div>
                </div>
                <Button
                  size="icon"
                  variant="ghost"
                  className="h-8 w-8 rounded-full hover:bg-amber-500/10 hover:text-amber-600 hover:scale-110 transition-all duration-300"
                  onClick={() => setActiveTab("leaderboard")}
                  title={t('nav.leaderboard')}
                >
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>

              {topLeaderboardPlayer ? (
                <div className="space-y-6">
                  {/* Player Name and Avatar */}
                  <div className="text-center p-4 rounded-xl bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-950/20 dark:to-yellow-950/20 border border-amber-200 dark:border-amber-800">
                    <div className="flex items-center justify-center mb-3">
                      <div className="bg-gradient-to-br from-amber-500 to-yellow-500 p-4 rounded-full shadow-lg">
                        <Crown className="h-6 w-6 text-white" aria-hidden="true" />
                      </div>
                    </div>
                    <div className="text-xl font-bold text-amber-700 dark:text-amber-300">{topLeaderboardPlayer.name}</div>
                    <div className="text-sm font-medium text-amber-600 dark:text-amber-400">{t('dashboard.currentLeader', 'Current Leader')}</div>
                  </div>

                  {/* Stats Grid */}
                  <div className="grid grid-cols-3 gap-3">
                    <div className="text-center p-3 rounded-lg bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border border-blue-200 dark:border-blue-800">
                      <Medal className="h-5 w-5 text-blue-600 mx-auto mb-1" />
                      <div className="text-lg font-bold text-blue-700 dark:text-blue-300">{topLeaderboardPlayer.avgRating}</div>
                      <div className="text-xs text-blue-600 dark:text-blue-400">{t('players.rating')}</div>
                    </div>
                    <div className="text-center p-3 rounded-lg bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border border-green-200 dark:border-green-800">
                      <Percent className="h-5 w-5 text-green-600 mx-auto mb-1" />
                      <div className="text-lg font-bold text-green-700 dark:text-green-300">{topLeaderboardPlayer.winRate.toFixed(1)}%</div>
                      <div className="text-xs text-green-600 dark:text-green-400">{t('players.winRate')}</div>
                    </div>
                    <div className="text-center p-3 rounded-lg bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-950/20 dark:to-violet-950/20 border border-purple-200 dark:border-purple-800">
                      <Calendar className="h-5 w-5 text-purple-600 mx-auto mb-1" />
                      <div className="text-lg font-bold text-purple-700 dark:text-purple-300">{topLeaderboardPlayer.played}</div>
                      <div className="text-xs text-purple-600 dark:text-purple-400">{t('players.played')}</div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8 space-y-4">
                  <div className="bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 p-6 rounded-full">
                    <Crown className="h-8 w-8 text-muted-foreground opacity-50" />
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium text-muted-foreground">{t('leaderboard.noPlayersFound')}</p>
                    <p className="text-xs text-muted-foreground mt-1">{t('dashboard.addPlayersToSeeLeader', 'Add players to see the leader')}</p>
                  </div>
                </div>
              )}
            </div>
          </EnhancedCard>

          {/* Chemistry Card - Enhanced */}
          <EnhancedCard hoverable className="lg:col-span-1 group">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="bg-gradient-to-br from-purple-500 to-pink-500 p-2 rounded-xl shadow-sm">
                    <Handshake className="h-5 w-5 text-white" />
                  </div>
                  <div className="font-semibold text-lg bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('chemistry.title', 'Chemistry')}</div>
                </div>
                <Button
                  size="icon"
                  variant="ghost"
                  className="h-8 w-8 rounded-full hover:bg-purple-500/10 hover:text-purple-600 hover:scale-110 transition-all duration-300"
                  onClick={() => setActiveTab("chemistry")}
                  title={t('nav.chemistry')}
                >
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>

              {matches.length > 0 ? (
                <div className="space-y-3">
                  {/* Duo Chemistry */}
                  {bestChemistry.duos.length > 0 && (
                    <Collapsible className="border rounded-md">
                      <CollapsibleTrigger className="flex items-center justify-between w-full p-2 hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-2">
                          <div className="bg-primary/10 p-1.5 rounded-full">
                            <Handshake className="h-3.5 w-3.5 text-primary" aria-hidden="true" />
                          </div>
                          <div className="text-sm font-medium">{t('chemistry.bestDuo', 'Best Duo')}</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-muted-foreground">
                            {bestChemistry.duos[0].winRate.toFixed(1)}%
                          </span>
                          <ChevronDown className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </CollapsibleTrigger>
                      <CollapsibleContent className="p-2 pt-0 border-t">
                        <div className="text-sm">
                          <div>
                            {bestChemistry.duos[0].players.map(id => getPlayerName(id)).join(" & ")}
                          </div>
                          <div className="flex items-center justify-between text-xs text-muted-foreground mt-1">
                            <span>{t('chemistry.winRate', 'Win Rate')}: <strong className="text-green-600">{bestChemistry.duos[0].winRate.toFixed(1)}%</strong></span>
                            <span>{t('players.won', 'Won')}: {bestChemistry.duos[0].wins}/{bestChemistry.duos[0].played}</span>
                          </div>
                        </div>
                      </CollapsibleContent>
                    </Collapsible>
                  )}

                  {/* Trio Chemistry */}
                  {bestChemistry.trios.length > 0 && (
                    <Collapsible className="border rounded-md">
                      <CollapsibleTrigger className="flex items-center justify-between w-full p-2 hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-2">
                          <div className="bg-primary/10 p-1.5 rounded-full">
                            <Users className="h-3.5 w-3.5 text-primary" aria-hidden="true" />
                          </div>
                          <div className="text-sm font-medium">{t('chemistry.bestTrio', 'Best Trio')}</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-muted-foreground">
                            {bestChemistry.trios[0].winRate.toFixed(1)}%
                          </span>
                          <ChevronDown className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </CollapsibleTrigger>
                      <CollapsibleContent className="p-2 pt-0 border-t">
                        <div className="text-sm">
                          <div>
                            {bestChemistry.trios[0].players.map(id => getPlayerName(id)).join(", ")}
                          </div>
                          <div className="flex items-center justify-between text-xs text-muted-foreground mt-1">
                            <span>{t('chemistry.winRate', 'Win Rate')}: <strong className="text-green-600">{bestChemistry.trios[0].winRate.toFixed(1)}%</strong></span>
                            <span>{t('players.won', 'Won')}: {bestChemistry.trios[0].wins}/{bestChemistry.trios[0].played}</span>
                          </div>
                        </div>
                      </CollapsibleContent>
                    </Collapsible>
                  )}

                  {/* Quad Chemistry */}
                  {bestChemistry.quads.length > 0 && (
                    <Collapsible className="border rounded-md">
                      <CollapsibleTrigger className="flex items-center justify-between w-full p-2 hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-2">
                          <div className="bg-primary/10 p-1.5 rounded-full">
                            <UsersRound className="h-3.5 w-3.5 text-primary" aria-hidden="true" />
                          </div>
                          <div className="text-sm font-medium">{t('chemistry.bestQuad', 'Best Quad')}</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-muted-foreground">
                            {bestChemistry.quads[0].winRate.toFixed(1)}%
                          </span>
                          <ChevronDown className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </CollapsibleTrigger>
                      <CollapsibleContent className="p-2 pt-0 border-t">
                        <div className="text-sm">
                          <div>
                            {bestChemistry.quads[0].players.map(id => getPlayerName(id)).join(", ")}
                          </div>
                          <div className="flex items-center justify-between text-xs text-muted-foreground mt-1">
                            <span>{t('chemistry.winRate', 'Win Rate')}: <strong className="text-green-600">{bestChemistry.quads[0].winRate.toFixed(1)}%</strong></span>
                            <span>{t('players.won', 'Won')}: {bestChemistry.quads[0].wins}/{bestChemistry.quads[0].played}</span>
                          </div>
                        </div>
                      </CollapsibleContent>
                    </Collapsible>
                  )}


                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-6 space-y-3">
                  <Handshake className="h-10 w-10 text-muted-foreground opacity-50" />
                  <p className="text-sm text-muted-foreground">{t('chemistry.noChemistryData')}</p>
                </div>
              )}
            </div>
          </EnhancedCard>

          {/* Player Selection Card */}
          {hasNoPlayers ? (
            <EmptyState
              icon={UserPlus}
              title={t('dashboard.noPlayersAdded')}
              description={t('dashboard.addPlayersDescription')}
              actionLabel={t('dashboard.addPlayers')}
              actionIcon={Plus}
              onAction={() => navigate('/players')}
              className="bg-card border-border lg:col-span-3"
            />
          ) : (
            <EnhancedCard className="lg:col-span-3">
              <div className="p-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                  <div className="font-medium text-lg">{t('dashboard.playerStats')}</div>
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                    <div className="relative w-full sm:w-[200px]">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        type="text"
                        placeholder={t('dashboard.searchPlayers')}
                        className="pl-8 h-9 text-sm w-full"
                        onChange={(e) => {
                          // Filter the dropdown options based on search
                          const searchTerm = e.target.value.toLowerCase();
                          if (searchTerm && players.length > 0) {
                            const matchedPlayer = players.find(p =>
                              p.name.toLowerCase().includes(searchTerm)
                            );
                            if (matchedPlayer) {
                              setSelectedPlayerId(matchedPlayer.id.toString());
                            }
                          }
                        }}
                      />
                    </div>
                    <div className="flex items-center gap-2 w-full sm:w-auto">
                      <Select
                        value={selectedPlayerId}
                        onValueChange={(value) => {
                          setSelectedPlayerId(value);
                          if (value) {
                            const player = players.find(p => p.id === parseInt(value));
                            if (player) {
                              toast({
                                title: t('players.playerSelected', 'Player Selected'),
                                description: t('players.viewingStatsFor', 'Viewing stats for {{name}}', { name: player.name })
                              });
                            }
                          }
                        }}
                      >
                        <SelectTrigger id="player-select" className="w-full sm:w-[150px] h-9">
                          <SelectValue placeholder={t('players.searchPlayers')} />
                        </SelectTrigger>
                        <SelectContent>
                          {players
                            .sort((a, b) => {
                              // Sort by number of matches played (descending)
                              const aPlayed = playerStatsWithWinRate.find(p => p.id === a.id)?.played || 0;
                              const bPlayed = playerStatsWithWinRate.find(p => p.id === b.id)?.played || 0;
                              return bPlayed - aPlayed;
                            })
                            .map((player) => {
                              const stats = playerStatsWithWinRate.find(p => p.id === player.id);
                              const gamesPlayed = stats?.played || 0;
                              return (
                                <SelectItem key={player.id} value={player.id.toString()}>
                                  {player.name} ({gamesPlayed})
                                </SelectItem>
                              );
                            })
                          }
                        </SelectContent>
                      </Select>
                      <User className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </div>
                </div>
              </div>
            </EnhancedCard>
          )}

        {isPlayerDataLoading ? (
          <div className="lg:col-span-3">
            <PlayerCardSkeleton />
          </div>
        ) : selectedPlayerData && (
          <EnhancedCard
            className="lg:col-span-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 dark:bg-gradient-to-r dark:from-gray-800 dark:to-gray-900 dark:border-gray-700 transition-colors duration-200"
            hoverable
          >
            <EnhancedCard.Header>
              <EnhancedCard.Title className="flex items-center gap-2">
                <User className="h-5 w-5 text-blue-700 dark:text-blue-400" />
                {selectedPlayerData.name} - {t('dashboard.playerSnapshot', 'Player Snapshot')}
              </EnhancedCard.Title>
            </EnhancedCard.Header>
            <EnhancedCard.Content className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4 text-sm">
              <div className="flex items-center gap-2">
                <ShieldCheck className="h-4 w-4 text-green-600 dark:text-green-400" />
                <span className="dark:text-gray-300">{t('players.record', 'Record')}: <strong className="text-green-700 dark:text-green-400">{selectedPlayerData.wins}W</strong> - <strong className="text-gray-600 dark:text-gray-400">{selectedPlayerData.draws}D</strong> - <strong className="text-red-700 dark:text-red-400">{selectedPlayerData.losses}L</strong> ({selectedPlayerData.played} {t('players.played', 'Played')})</span>
              </div>
              <div className="flex items-center gap-2">
                <Sparkles className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                <span className="dark:text-gray-300">{t('chemistry.winRate', 'Win Rate')}: <strong>{selectedPlayerData.winRate.toFixed(1)}%</strong></span>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                <span className="dark:text-gray-300">{t('players.averageRating', 'Avg Rating')}: <strong>{selectedPlayerData.avgRating}</strong></span>
              </div>
              <div className="flex items-center gap-2">
                <Trophy className="h-4 w-4 text-amber-500 dark:text-amber-400" />
                <span className="dark:text-gray-300">{t('leaderboard.rank', 'Leaderboard Rank')}: {
                  selectedPlayerData.rank > 0
                    ? <strong>#{selectedPlayerData.rank}</strong>
                    : <span className="text-muted-foreground">({t('leaderboard.notRanked', 'Not ranked')} - {t('leaderboard.minGames', 'min. games')}: {minGamesForLeaderboard})</span>
                }</span>
              </div>
              <div className="flex items-center gap-2">
                <Trophy className={`h-4 w-4 ${
                  selectedPlayerData.worldCupRunData.status === 'champion' ? 'text-amber-500' :
                  selectedPlayerData.worldCupRunData.status === 'qualified' ? 'text-green-500' :
                  selectedPlayerData.worldCupRunData.status === 'eliminated' ? 'text-red-500' :
                  'text-gray-400'
                }`} />
                <span className="dark:text-gray-300" title={t('players.worldCupRunTooltip', 'Simulated World Cup tournament based on last 7 matches')}>
                  {t('players.worldCupRun', 'World Cup Run')}: {' '}

                  {selectedPlayerData.worldCupRunData.status === 'champion' ? (
                    <strong className="text-amber-500 dark:text-amber-400">{t('players.worldChampion', 'World Champion')}</strong>
                  ) : selectedPlayerData.worldCupRunData.status === 'qualified' ? (
                    <span>
                      <strong className="text-green-600 dark:text-green-400">
                        {t('players.qualifiedWithPoints', 'Knockouts ({{wins}}/4 wins)', { wins: selectedPlayerData.worldCupRunData.knockoutMatchesWon })}
                      </strong>
                    </span>
                  ) : selectedPlayerData.worldCupRunData.status === 'eliminated' ? (
                    <span>
                      <strong className="text-red-600 dark:text-red-400">{t('players.eliminated', 'Eliminated')}</strong>
                    </span>
                  ) : selectedPlayerData.worldCupRunData.status === 'not-started' ? (
                    <span>
                      <strong className="text-gray-500">{t('players.notStarted', 'Not Started')}</strong>
                    </span>
                  ) : (
                    <span>
                      <strong className="text-gray-500">
                        {t('players.groupStageOngoing', 'Qualifying ({{points}}/9 pts)', { points: selectedPlayerData.worldCupRunData.groupStagePoints })}
                      </strong>
                    </span>
                  )}
                </span>
              </div>

              {/* Display completed World Cup Runs with dates */}
              {selectedPlayerData.worldCupRunData.completedRuns.length > 0 && (
                <div className="flex flex-col gap-1 mt-2 ml-6">
                  <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                    {t('players.worldCupRunsWon', 'World Cup Runs Won')}:
                  </span>
                  <div className="flex flex-wrap gap-2">
                    {selectedPlayerData.worldCupRunData.completedRuns.map((run, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-100 border-amber-200 dark:border-amber-800"
                      >
                        🏆 {format(run.date, "MMM d, yyyy")}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex items-center gap-2 mt-2">
                <Flame className="h-4 w-4 text-orange-500 dark:text-orange-400" />
                <span className="dark:text-gray-300" title={t('players.bestWinStreakTooltip', 'Best consecutive win streak')}>
                  {t('players.bestWinStreak', 'Best Win Streak')}: <strong>{selectedPlayerData.bestWinStreak}</strong>
                </span>
              </div>
              <div className="flex items-center gap-2">
                <History className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                <span className="dark:text-gray-300">{t('matches.lastMatch', 'Last Match')}:
                  {selectedPlayerData.lastMatch && selectedPlayerData.lastMatch.date instanceof Date ? (
                    <Badge variant="outline" className="dark:border-gray-600 dark:text-gray-300">{format(selectedPlayerData.lastMatch.date, "d/M/yyyy")}</Badge>
                  ) : (
                    <span>{t('common.na', 'N/A')}</span>
                  )}
                </span>
              </div>
              <div className="col-span-full mt-2">
                <h4 className="font-medium mb-2 dark:text-gray-300">{t('chemistry.summary', 'Chemistry Stats')}</h4>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Badge variant="outline" className="mb-1 dark:border-gray-600 dark:text-gray-300">{t('chemistry.duos', 'Duos')}</Badge>
                    <div className="text-xs space-y-2 dark:text-gray-400">
                      <div className="font-medium dark:text-gray-300">{t('chemistry.bestWith', 'Best With')}:</div>
                      {selectedPlayerData.bestChemistry.duos.map((duo, i) => (
                        <div key={`duo-${i}`} className="pl-2 border-l-2 border-blue-200 dark:border-blue-800">
                          {duo.players.filter(id => id !== parseInt(selectedPlayerId)).map(id => getPlayerName(id)).join(', ')}
                          <br />
                          <span className="text-green-600 dark:text-green-400">{duo.winRate.toFixed(1)}%</span> ({duo.played} games)
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <Badge variant="outline" className="mb-1 dark:border-gray-600 dark:text-gray-300">{t('chemistry.trios', 'Trios')}</Badge>
                    <div className="text-xs space-y-2 dark:text-gray-400">
                      <div className="font-medium dark:text-gray-300">{t('chemistry.bestWith', 'Best With')}:</div>
                      {selectedPlayerData.bestChemistry.trios.map((trio, i) => (
                        <div key={`trio-${i}`} className="pl-2 border-l-2 border-blue-200 dark:border-blue-800">
                          {trio.players.filter(id => id !== parseInt(selectedPlayerId)).map(id => getPlayerName(id)).join(', ')}
                          <br />
                          <span className="text-green-600 dark:text-green-400">{trio.winRate.toFixed(1)}%</span> ({trio.played} games)
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <Badge variant="outline" className="mb-1 dark:border-gray-600 dark:text-gray-300">{t('chemistry.quads', 'Quads')}</Badge>
                    <div className="text-xs space-y-2 dark:text-gray-400">
                      <div className="font-medium dark:text-gray-300">{t('chemistry.bestWith', 'Best With')}:</div>
                      {selectedPlayerData.bestChemistry.quads.map((quad, i) => (
                        <div key={`quad-${i}`} className="pl-2 border-l-2 border-blue-200 dark:border-blue-800">
                          {quad.players.filter(id => id !== parseInt(selectedPlayerId)).map(id => getPlayerName(id)).join(', ')}
                          <br />
                          <span className="text-green-600 dark:text-green-400">{quad.winRate.toFixed(1)}%</span> ({quad.played} games)
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </EnhancedCard.Content>
          </EnhancedCard>
        )}


        </div>
          </TabsContent>

          <TabsContent value="leaderboard">
            <DashboardLeaderboard players={players} matches={matches} />
          </TabsContent>

          <TabsContent value="chemistry">
            <DashboardChemistry players={players} matches={matches} />
          </TabsContent>

          <TabsContent value="team-generator">
            <DashboardTeamGenerator
              players={playerStatsWithWinRate}
              matches={matches}
              onSaveMatch={async (newMatch) => {
                try {
                  // Add group_id to the match
                  const matchWithGroup = {
                    ...newMatch,
                    group_id: currentGroup?.id,
                    match_date: new Date().toISOString()
                  };

                  const { data, error } = await supabase
                    .from('matches')
                    .insert([matchWithGroup])
                    .select();

                  if (error) throw error;

                  // Update local state
                  setMatches(prev => [...prev, {
                    ...matchWithGroup,
                    id: data[0].id,
                    date: new Date()
                  } as Match]);

                } catch (error) {
                  console.error("Error saving match:", error);
                  toast({
                    title: t('matches.errorSaving'),
                    description: error.message,
                    variant: "destructive"
                  });
                }
              }}
            />
          </TabsContent>
        </Tabs>
      )}
      </div>
    </UnifiedLayout>
  );
};

export default DashboardPage;

