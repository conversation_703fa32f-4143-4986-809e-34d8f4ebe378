import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { configDefaults } from 'vitest/config';
import { visualizer } from 'rollup-plugin-visualizer';
import { VitePWA } from 'vite-plugin-pwa';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "localhost",
    port: 5173,
    strictPort: true,
    open: true,
  },
  plugins: [
    react(),
    mode === 'production' && visualizer({
      open: false,
      gzipSize: true,
      brotliSize: true,
      filename: 'dist/stats.html',
    }),
    VitePWA({
      registerType: 'autoUpdate',
      includeAssets: ['favicon.svg', 'favicon.ico', 'robots.txt', 'icons/*.png'],
      manifest: false, // We're using our own manifest.json
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,jpg,jpeg,gif,webp}'],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
            handler: 'CacheFirst',
            options: {
              cacheName: 'google-fonts-cache',
              expiration: {
                maxEntries: 10,
                maxAgeSeconds: 60 * 60 * 24 * 365 // <== 365 days
              },
              cacheableResponse: {
                statuses: [0, 200]
              }
            }
          },
          {
            urlPattern: /^https:\/\/fonts\.gstatic\.com\/.*/i,
            handler: 'CacheFirst',
            options: {
              cacheName: 'gstatic-fonts-cache',
              expiration: {
                maxEntries: 10,
                maxAgeSeconds: 60 * 60 * 24 * 365 // <== 365 days
              },
              cacheableResponse: {
                statuses: [0, 200]
              },
            }
          }
        ]
      }
    }),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json'],
  },
  build: {
    sourcemap: true,
    target: 'es2020',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Core libraries
          if (id.includes('node_modules/react') || id.includes('node_modules/react-dom')) {
            return 'vendor-react';
          }
          if (id.includes('node_modules/@supabase')) {
            return 'vendor-supabase';
          }
          if (id.includes('node_modules/@tanstack/react-query')) {
            return 'vendor-react-query';
          }
          if (id.includes('node_modules/date-fns')) {
            return 'vendor-date-fns';
          }
          if (id.includes('node_modules/lucide-react')) {
            return 'vendor-lucide';
          }

          // UI components
          if (id.includes('/components/ui/')) {
            return 'ui-components';
          }

          // Feature-based code splitting
          if (id.includes('/pages/Dashboard')) {
            return 'page-dashboard';
          }
          if (id.includes('/pages/Players')) {
            return 'page-players';
          }
          if (id.includes('/pages/Matches')) {
            return 'page-matches';
          }
          if (id.includes('/pages/TeamGenerator')) {
            return 'page-team-generator';
          }
          if (id.includes('/pages/Chemistry')) {
            return 'page-chemistry';
          }
          if (id.includes('/pages/Leaderboard')) {
            return 'page-leaderboard';
          }
          if (id.includes('/pages/shared/')) {
            return 'page-shared';
          }

          // Auth-related code
          if (id.includes('/context/Auth') || id.includes('/components/auth/')) {
            return 'auth';
          }

          // Offline and cache-related code
          if (id.includes('/lib/offline/') || id.includes('/lib/cache/')) {
            return 'offline-support';
          }
        },
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
      },
    },
  },
  optimizeDeps: {
    include: ['react', 'react-dom', '@tanstack/react-query', 'date-fns', 'lucide-react'],
    esbuildOptions: {
      target: 'es2020',
    },
  },
  build: {
    commonjsOptions: {
      include: [/node_modules/],
      transformMixedEsModules: true,
    },
  },
  esbuild: {
    target: 'es2020',
    supported: {
      'top-level-await': true,
    },
  },
  // Test config will be added by vitest.config.ts
}));
